import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { UserConfig, DEFAULT_CONFIG, SUPPORTED_PROVIDERS } from '../types';

export class ConfigManager {
  private configDir: string;
  private configFile: string;
  private config: UserConfig;

  constructor() {
    this.configDir = path.join(os.homedir(), '.ai-cli-terminal');
    this.configFile = path.join(this.configDir, 'config.json');
    this.config = this.loadConfig();
  }

  private loadConfig(): UserConfig {
    try {
      if (fs.existsSync(this.configFile)) {
        const configData = fs.readJsonSync(this.configFile);
        return { ...DEFAULT_CONFIG, ...configData } as UserConfig;
      }
    } catch (error) {
      console.warn('Failed to load config, using defaults:', error);
    }
    
    return {
      defaultProvider: 'openai',
      providers: {},
      preferences: DEFAULT_CONFIG.preferences!,
      history: [],
    };
  }

  public async saveConfig(): Promise<void> {
    try {
      await fs.ensureDir(this.configDir);
      await fs.writeJson(this.configFile, this.config, { spaces: 2 });
    } catch (error) {
      throw new Error(`Failed to save config: ${error}`);
    }
  }

  public getConfig(): UserConfig {
    return { ...this.config };
  }

  public async setProvider(
    providerName: string,
    apiKey: string,
    defaultModel?: string,
    baseUrl?: string
  ): Promise<void> {
    if (!SUPPORTED_PROVIDERS[providerName]) {
      throw new Error(`Unsupported provider: ${providerName}`);
    }

    const provider = SUPPORTED_PROVIDERS[providerName];
    
    if (!this.config.providers) {
      this.config.providers = {};
    }

    this.config.providers[providerName] = {
      apiKey,
      defaultModel: defaultModel || provider.models[0],
      ...(baseUrl && { baseUrl }),
    };

    await this.saveConfig();
  }

  public async setDefaultProvider(providerName: string): Promise<void> {
    if (!SUPPORTED_PROVIDERS[providerName]) {
      throw new Error(`Unsupported provider: ${providerName}`);
    }

    if (!this.config.providers[providerName]) {
      throw new Error(`Provider ${providerName} not configured. Please set API key first.`);
    }

    this.config.defaultProvider = providerName;
    await this.saveConfig();
  }

  public getProviderConfig(providerName?: string) {
    const provider = providerName || this.config.defaultProvider;
    return this.config.providers[provider];
  }

  public isProviderConfigured(providerName?: string): boolean {
    const provider = providerName || this.config.defaultProvider;
    const providerConfig = this.config.providers[provider];
    return !!(providerConfig && providerConfig.apiKey);
  }

  public getConfiguredProviders(): string[] {
    return Object.keys(this.config.providers).filter(provider => 
      this.config.providers[provider].apiKey
    );
  }

  public async updatePreferences(preferences: Partial<UserConfig['preferences']>): Promise<void> {
    this.config.preferences = { ...this.config.preferences, ...preferences };
    await this.saveConfig();
  }

  public async addToHistory(message: any): Promise<void> {
    if (!this.config.history) {
      this.config.history = [];
    }
    
    this.config.history.push(message);
    
    // Keep only last 100 messages
    if (this.config.history.length > 100) {
      this.config.history = this.config.history.slice(-100);
    }
    
    await this.saveConfig();
  }

  public getHistory(): any[] {
    return this.config.history || [];
  }

  public async clearHistory(): Promise<void> {
    this.config.history = [];
    await this.saveConfig();
  }

  public async resetConfig(): Promise<void> {
    this.config = {
      defaultProvider: 'openai',
      providers: {},
      preferences: DEFAULT_CONFIG.preferences!,
      history: [],
    };
    await this.saveConfig();
  }

  public getConfigDir(): string {
    return this.configDir;
  }

  public async ensureConfigDir(): Promise<void> {
    await fs.ensureDir(this.configDir);
  }
}
