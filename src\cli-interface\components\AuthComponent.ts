import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import { ConfigManager } from '../../core/config/ConfigManager';
import { SUPPORTED_PROVIDERS } from '../../core/types';
import { CLIComponent } from '../../core/types';

export class AuthComponent implements CLIComponent {
  private configManager: ConfigManager;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
  }

  public async render(): Promise<void> {
    console.clear();
    
    // Display welcome banner
    const banner = boxen(
      chalk.bold.cyan('🤖 AI CLI Terminal\n') +
      chalk.gray('Configure your AI providers to get started'),
      {
        padding: 1,
        margin: 1,
        borderStyle: 'round',
        borderColor: 'cyan',
        textAlignment: 'center'
      }
    );
    
    console.log(banner);

    // Check if any providers are configured
    const configuredProviders = this.configManager.getConfiguredProviders();
    
    if (configuredProviders.length === 0) {
      console.log(chalk.yellow('⚠️  No AI providers configured. Let\'s set one up!\n'));
      await this.setupFirstProvider();
    } else {
      await this.showMainMenu(configuredProviders);
    }
  }

  private async setupFirstProvider(): Promise<void> {
    const providerChoices = Object.values(SUPPORTED_PROVIDERS).map(provider => ({
      name: `${provider.displayName} (${provider.models.join(', ')})`,
      value: provider.name,
    }));

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Choose your AI provider:',
        choices: providerChoices,
      },
    ]);

    await this.configureProvider(provider);
  }

  private async showMainMenu(configuredProviders: string[]): Promise<void> {
    const config = this.configManager.getConfig();
    
    console.log(chalk.green('✅ Configured providers:'));
    configuredProviders.forEach(provider => {
      const isDefault = provider === config.defaultProvider;
      const providerConfig = this.configManager.getProviderConfig(provider);
      const displayName = SUPPORTED_PROVIDERS[provider].displayName;
      const model = providerConfig.defaultModel;
      
      console.log(
        `  ${isDefault ? chalk.bold.green('●') : chalk.gray('○')} ${displayName} (${model})${
          isDefault ? chalk.gray(' - default') : ''
        }`
      );
    });

    console.log();

    const choices = [
      { name: '🚀 Start Terminal', value: 'start' },
      { name: '➕ Add Provider', value: 'add' },
      { name: '⚙️  Configure Provider', value: 'configure' },
      { name: '🔄 Change Default Provider', value: 'default' },
      { name: '🗑️  Remove Provider', value: 'remove' },
      { name: '⚙️  Preferences', value: 'preferences' },
      { name: '🔄 Reset Configuration', value: 'reset' },
      { name: '❌ Exit', value: 'exit' },
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices,
      },
    ]);

    await this.handleMenuAction(action, configuredProviders);
  }

  private async handleMenuAction(action: string, configuredProviders: string[]): Promise<void> {
    switch (action) {
      case 'start':
        if (configuredProviders.length === 0) {
          console.log(chalk.red('❌ No providers configured. Please add a provider first.'));
          await this.render();
        }
        return; // Exit auth component to start terminal

      case 'add':
        const unconfiguredProviders = Object.keys(SUPPORTED_PROVIDERS).filter(
          p => !configuredProviders.includes(p)
        );
        
        if (unconfiguredProviders.length === 0) {
          console.log(chalk.yellow('All providers are already configured!'));
          await this.waitForKeyPress();
          await this.render();
          return;
        }

        const { newProvider } = await inquirer.prompt([
          {
            type: 'list',
            name: 'newProvider',
            message: 'Choose provider to add:',
            choices: unconfiguredProviders.map(p => ({
              name: SUPPORTED_PROVIDERS[p].displayName,
              value: p,
            })),
          },
        ]);
        
        await this.configureProvider(newProvider);
        break;

      case 'configure':
        const { providerToConfig } = await inquirer.prompt([
          {
            type: 'list',
            name: 'providerToConfig',
            message: 'Choose provider to configure:',
            choices: configuredProviders.map(p => ({
              name: SUPPORTED_PROVIDERS[p].displayName,
              value: p,
            })),
          },
        ]);
        
        await this.configureProvider(providerToConfig);
        break;

      case 'default':
        const { defaultProvider } = await inquirer.prompt([
          {
            type: 'list',
            name: 'defaultProvider',
            message: 'Choose default provider:',
            choices: configuredProviders.map(p => ({
              name: SUPPORTED_PROVIDERS[p].displayName,
              value: p,
            })),
          },
        ]);
        
        await this.configManager.setDefaultProvider(defaultProvider);
        console.log(chalk.green(`✅ Default provider set to ${SUPPORTED_PROVIDERS[defaultProvider].displayName}`));
        await this.waitForKeyPress();
        await this.render();
        break;

      case 'remove':
        await this.removeProvider(configuredProviders);
        break;

      case 'preferences':
        await this.configurePreferences();
        break;

      case 'reset':
        const { confirmReset } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirmReset',
            message: 'Are you sure you want to reset all configuration?',
            default: false,
          },
        ]);
        
        if (confirmReset) {
          await this.configManager.resetConfig();
          console.log(chalk.green('✅ Configuration reset successfully'));
          await this.waitForKeyPress();
          await this.render();
        } else {
          await this.render();
        }
        break;

      case 'exit':
        console.log(chalk.gray('Goodbye! 👋'));
        process.exit(0);
        break;

      default:
        await this.render();
    }
  }

  private async configureProvider(providerName: string): Promise<void> {
    const provider = SUPPORTED_PROVIDERS[providerName];
    const existingConfig = this.configManager.getProviderConfig(providerName);

    console.log(chalk.cyan(`\n🔧 Configuring ${provider.displayName}`));

    const questions: any[] = [
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter your ${provider.displayName} API key:`,
        default: existingConfig?.apiKey,
        validate: (input: string) => input.trim().length > 0 || 'API key is required',
      },
      {
        type: 'list',
        name: 'model',
        message: 'Choose default model:',
        choices: provider.models,
        default: existingConfig?.defaultModel || provider.models[0],
      },
    ];

    if (provider.baseUrl) {
      questions.push({
        type: 'input',
        name: 'baseUrl',
        message: 'Base URL (optional):',
        default: existingConfig?.baseUrl || provider.baseUrl,
      });
    }

    const answers = await inquirer.prompt(questions);

    try {
      await this.configManager.setProvider(
        providerName,
        answers.apiKey,
        answers.model,
        answers.baseUrl
      );

      // Set as default if it's the first provider
      const configuredProviders = this.configManager.getConfiguredProviders();
      if (configuredProviders.length === 1) {
        await this.configManager.setDefaultProvider(providerName);
      }

      console.log(chalk.green(`✅ ${provider.displayName} configured successfully!`));
      await this.waitForKeyPress();
      await this.render();
    } catch (error) {
      console.log(chalk.red(`❌ Failed to configure provider: ${error}`));
      await this.waitForKeyPress();
      await this.render();
    }
  }

  private async removeProvider(configuredProviders: string[]): Promise<void> {
    const { providerToRemove } = await inquirer.prompt([
      {
        type: 'list',
        name: 'providerToRemove',
        message: 'Choose provider to remove:',
        choices: configuredProviders.map(p => ({
          name: SUPPORTED_PROVIDERS[p].displayName,
          value: p,
        })),
      },
    ]);

    const { confirmRemove } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmRemove',
        message: `Are you sure you want to remove ${SUPPORTED_PROVIDERS[providerToRemove].displayName}?`,
        default: false,
      },
    ]);

    if (confirmRemove) {
      const config = this.configManager.getConfig();
      delete config.providers[providerToRemove];
      
      // If removing default provider, set new default
      if (config.defaultProvider === providerToRemove) {
        const remainingProviders = Object.keys(config.providers);
        if (remainingProviders.length > 0) {
          config.defaultProvider = remainingProviders[0];
        }
      }

      await this.configManager.saveConfig();
      console.log(chalk.green(`✅ ${SUPPORTED_PROVIDERS[providerToRemove].displayName} removed successfully`));
      await this.waitForKeyPress();
    }

    await this.render();
  }

  private async configurePreferences(): Promise<void> {
    const config = this.configManager.getConfig();
    const prefs = config.preferences;

    const answers = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'requireToolConfirmation',
        message: 'Require confirmation before executing tools?',
        default: prefs.requireToolConfirmation,
      },
      {
        type: 'confirm',
        name: 'enableShellTools',
        message: 'Enable shell command tools?',
        default: prefs.enableShellTools,
      },
      {
        type: 'confirm',
        name: 'enableWebTools',
        message: 'Enable web tools (fetch, search)?',
        default: prefs.enableWebTools,
      },
      {
        type: 'confirm',
        name: 'enableFileTools',
        message: 'Enable file system tools?',
        default: prefs.enableFileTools,
      },
      {
        type: 'confirm',
        name: 'enableMemoryTools',
        message: 'Enable memory tools?',
        default: prefs.enableMemoryTools,
      },
      {
        type: 'confirm',
        name: 'sandboxMode',
        message: 'Enable sandbox mode (restricted operations)?',
        default: prefs.sandboxMode,
      },
    ]);

    await this.configManager.updatePreferences(answers);
    console.log(chalk.green('✅ Preferences updated successfully!'));
    await this.waitForKeyPress();
    await this.render();
  }

  private async waitForKeyPress(): Promise<void> {
    await inquirer.prompt([
      {
        type: 'input',
        name: 'continue',
        message: 'Press Enter to continue...',
      },
    ]);
  }
}
