// Core type definitions for the AI CLI Terminal system

export interface AIProvider {
  name: string;
  displayName: string;
  models: string[];
  requiresApiKey: boolean;
  baseUrl?: string;
}

export interface Configuration {
  provider: string;
  model: string;
  apiKey: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
}

export interface UserConfig {
  defaultProvider: string;
  providers: Record<string, {
    apiKey: string;
    defaultModel: string;
    baseUrl?: string;
  }>;
  preferences: {
    requireToolConfirmation: boolean;
    enableShellTools: boolean;
    enableWebTools: boolean;
    enableFileTools: boolean;
    enableMemoryTools: boolean;
    maxFileSize: number;
    sandboxMode: boolean;
  };
  history: ChatMessage[];
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  provider?: string;
  model?: string;
  toolCalls?: ToolCall[];
}

export interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
  result?: any;
  approved?: boolean;
  timestamp: Date;
}

export interface ToolResult {
  success: boolean;
  content: string;
  displayContent?: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface BaseTool {
  name: string;
  description: string;
  parameters: Record<string, any>;
  requiresConfirmation: boolean;
  validate(params: Record<string, any>): Promise<boolean>;
  execute(params: Record<string, any>): Promise<ToolResult>;
  shouldConfirmExecute?(params: Record<string, any>): Promise<boolean>;
}

export interface ProviderClient {
  name: string;
  isConfigured(): boolean;
  sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: {
      model?: string;
      maxTokens?: number;
      temperature?: number;
      stream?: boolean;
    }
  ): Promise<ChatMessage>;
  streamMessage?(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: {
      model?: string;
      maxTokens?: number;
      temperature?: number;
    }
  ): AsyncGenerator<string, ChatMessage>;
}

export interface CLIComponent {
  render(): Promise<void>;
  handleInput?(input: string): Promise<void>;
  cleanup?(): Promise<void>;
}

export const SUPPORTED_PROVIDERS: Record<string, AIProvider> = {
  openai: {
    name: 'openai',
    displayName: 'OpenAI',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    requiresApiKey: true,
  },
  anthropic: {
    name: 'anthropic',
    displayName: 'Anthropic',
    models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
    requiresApiKey: true,
  },
  google: {
    name: 'google',
    displayName: 'Google',
    models: ['gemini-pro', 'gemini-pro-vision'],
    requiresApiKey: true,
  },
  deepseek: {
    name: 'deepseek',
    displayName: 'Deepseek',
    models: ['deepseek-chat', 'deepseek-coder'],
    requiresApiKey: true,
    baseUrl: 'https://api.deepseek.com/v1',
  },
};

export const DEFAULT_CONFIG: Partial<UserConfig> = {
  defaultProvider: 'openai',
  preferences: {
    requireToolConfirmation: true,
    enableShellTools: true,
    enableWebTools: true,
    enableFileTools: true,
    enableMemoryTools: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    sandboxMode: false,
  },
  history: [],
};
