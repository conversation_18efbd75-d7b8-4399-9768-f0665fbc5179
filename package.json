{"name": "ai-cli-terminal", "version": "1.0.0", "description": "A CLI Terminal system powered by AI with support for multiple providers (Deepseek, OpenAI, Anthropic, Google)", "main": "dist/index.js", "bin": {"ai-cli": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["ai", "cli", "terminal", "openai", "anthropic", "google", "deepseek", "llm"], "author": "AI CLI Terminal", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@google/generative-ai": "^0.15.0", "axios": "^1.6.0", "boxen": "^7.1.1", "chalk": "^5.3.0", "commander": "^11.1.0", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "glob": "^10.3.10", "ignore": "^5.3.0", "inquirer": "^9.2.12", "openai": "^4.52.7", "ora": "^7.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.5", "@types/node": "^20.8.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}