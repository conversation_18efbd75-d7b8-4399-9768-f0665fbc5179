# AI CLI Terminal

A powerful CLI Terminal system powered by AI with support for multiple providers including Deepseek, OpenAI, Anthropic, and Google.

## Features

- 🤖 **Multi-Provider AI Support**: Deepseek, OpenAI, Anthropic, Google
- 🔧 **Comprehensive Tool System**: File operations, shell commands, web tools, memory management
- 🔐 **Secure Authentication**: API key management and configuration
- 🎯 **Interactive CLI**: Beautiful terminal interface with confirmation prompts
- 📁 **File System Tools**: Read, write, edit, search, and manage files
- 🌐 **Web Integration**: Fetch content and perform web searches
- 💾 **Memory Management**: Persistent AI memory across sessions
- 🔌 **MCP Support**: Model Context Protocol for extensible tools

## Quick Start

1. **Installation**
   ```bash
   npm install
   npm run build
   ```

2. **Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Run**
   ```bash
   npm start
   # or
   npm run dev
   ```

## Project Structure

```
src/
├── cli-interface/          # CLI UI/UX components
│   ├── components/         # Reusable CLI components
│   └── utils/             # CLI utilities
└── core/                  # Core engine and logic
    ├── providers/         # AI provider implementations
    ├── tools/            # Tool system implementation
    ├── config/           # Configuration management
    └── types/            # TypeScript type definitions
```

## Usage

The system starts with an authentication component where you can configure your preferred AI provider, API key, and model. Once configured, you can interact with the AI through the terminal interface.

### Supported Providers

- **OpenAI**: GPT-4, GPT-3.5-turbo
- **Anthropic**: Claude-3, Claude-2
- **Google**: Gemini Pro, Gemini Pro Vision
- **Deepseek**: Deepseek Chat, Deepseek Coder

### Available Tools

- **File System**: List, read, write, edit, search files
- **Shell Commands**: Execute system commands with sandboxing
- **Web Tools**: Fetch web content, perform Google searches
- **Memory**: Save and retrieve persistent information
- **MCP**: Extensible tool discovery and execution

## Development

```bash
npm run dev     # Development mode
npm run build   # Build for production
npm test        # Run tests
npm run lint    # Lint code
```

## License

MIT
