import chalk from 'chalk';
import boxen from 'boxen';
import ora, { Ora } from 'ora';
import inquirer from 'inquirer';

export class CLIUtils {
  private static spinner: Ora | null = null;

  public static showBanner(title: string, subtitle?: string): void {
    const content = chalk.bold.cyan(title) + (subtitle ? '\n' + chalk.gray(subtitle) : '');
    
    const banner = boxen(content, {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'cyan',
      textAlignment: 'center'
    });
    
    console.log(banner);
  }

  public static showSuccess(message: string): void {
    console.log(chalk.green('✅ ' + message));
  }

  public static showError(message: string): void {
    console.log(chalk.red('❌ ' + message));
  }

  public static showWarning(message: string): void {
    console.log(chalk.yellow('⚠️  ' + message));
  }

  public static showInfo(message: string): void {
    console.log(chalk.blue('ℹ️  ' + message));
  }

  public static showDebug(message: string): void {
    console.log(chalk.gray('🐛 ' + message));
  }

  public static startSpinner(text: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }
    this.spinner = ora(text).start();
  }

  public static updateSpinner(text: string): void {
    if (this.spinner) {
      this.spinner.text = text;
    }
  }

  public static stopSpinner(success: boolean = true, message?: string): void {
    if (this.spinner) {
      if (success) {
        this.spinner.succeed(message);
      } else {
        this.spinner.fail(message);
      }
      this.spinner = null;
    }
  }

  public static async confirmAction(message: string, defaultValue: boolean = false): Promise<boolean> {
    const { confirmed } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmed',
        message,
        default: defaultValue,
      },
    ]);
    return confirmed;
  }

  public static async selectFromList<T>(
    message: string,
    choices: Array<{ name: string; value: T }>,
    defaultValue?: T
  ): Promise<T> {
    const { selected } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selected',
        message,
        choices,
        default: defaultValue,
      },
    ]);
    return selected;
  }

  public static async getInput(
    message: string,
    defaultValue?: string,
    validate?: (input: string) => boolean | string
  ): Promise<string> {
    const { input } = await inquirer.prompt([
      {
        type: 'input',
        name: 'input',
        message,
        default: defaultValue,
        validate,
      },
    ]);
    return input;
  }

  public static async getPassword(message: string): Promise<string> {
    const { password } = await inquirer.prompt([
      {
        type: 'password',
        name: 'password',
        message,
        validate: (input: string) => input.trim().length > 0 || 'Password is required',
      },
    ]);
    return password;
  }

  public static formatCodeBlock(code: string, language: string = ''): string {
    const lines = code.split('\n');
    const formattedLines = lines.map((line, index) => {
      const lineNumber = (index + 1).toString().padStart(3, ' ');
      return chalk.gray(`${lineNumber} │ `) + line;
    });
    
    return boxen(
      chalk.cyan(`${language}\n`) + formattedLines.join('\n'),
      {
        padding: 1,
        borderStyle: 'round',
        borderColor: 'gray',
      }
    );
  }

  public static formatDiff(oldContent: string, newContent: string): string {
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    
    const maxLines = Math.max(oldLines.length, newLines.length);
    const diffLines: string[] = [];
    
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';
      
      if (oldLine !== newLine) {
        if (oldLine) {
          diffLines.push(chalk.red(`- ${oldLine}`));
        }
        if (newLine) {
          diffLines.push(chalk.green(`+ ${newLine}`));
        }
      } else if (oldLine) {
        diffLines.push(chalk.gray(`  ${oldLine}`));
      }
    }
    
    return boxen(
      chalk.bold('Diff Preview:\n') + diffLines.join('\n'),
      {
        padding: 1,
        borderStyle: 'round',
        borderColor: 'yellow',
      }
    );
  }

  public static formatTable(headers: string[], rows: string[][]): string {
    const columnWidths = headers.map((header, index) => {
      const maxRowWidth = Math.max(...rows.map(row => (row[index] || '').length));
      return Math.max(header.length, maxRowWidth);
    });

    const formatRow = (row: string[], isHeader: boolean = false) => {
      const formattedCells = row.map((cell, index) => {
        const width = columnWidths[index];
        const paddedCell = (cell || '').padEnd(width);
        return isHeader ? chalk.bold.cyan(paddedCell) : paddedCell;
      });
      return '│ ' + formattedCells.join(' │ ') + ' │';
    };

    const separator = '├' + columnWidths.map(width => '─'.repeat(width + 2)).join('┼') + '┤';
    const topBorder = '┌' + columnWidths.map(width => '─'.repeat(width + 2)).join('┬') + '┐';
    const bottomBorder = '└' + columnWidths.map(width => '─'.repeat(width + 2)).join('┴') + '┘';

    const lines = [
      topBorder,
      formatRow(headers, true),
      separator,
      ...rows.map(row => formatRow(row)),
      bottomBorder,
    ];

    return lines.join('\n');
  }

  public static async waitForKeyPress(message: string = 'Press Enter to continue...'): Promise<void> {
    await inquirer.prompt([
      {
        type: 'input',
        name: 'continue',
        message,
      },
    ]);
  }

  public static clearScreen(): void {
    console.clear();
  }

  public static printSeparator(char: string = '─', length: number = 50): void {
    console.log(chalk.gray(char.repeat(length)));
  }

  public static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  public static formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  }

  public static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }
}
